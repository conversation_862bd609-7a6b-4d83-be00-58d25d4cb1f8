package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// FoodSpecService 规格业务逻辑
type FoodSpecService struct {
	DB    *gorm.DB
	Trans *util.Trans
}

// GetList 获取规格列表(不分页，显示关联美食数量)
func (serv *FoodSpecService) GetList(ctx context.Context, merchantNo string, req *request.FoodSpecListRequest) ([]*model.FoodSpecModel, error) {
	var list []*model.FoodSpecModel

	db := util.GetDB(ctx, serv.DB).
		Select("food_specs.*, (SELECT COUNT(*) FROM foods WHERE foods.spec_id = food_specs.id AND foods.deleted_at IS NULL) as foods_count").
		Where("merchant_no = ? AND deleted_at IS NULL", merchantNo)

	// 添加搜索条件
	if req != nil && req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		db = db.Where("(name_zh LIKE ? OR name_ug LIKE ?)", searchPattern, searchPattern)
	}

	return list, db.Order("sort ASC, id ASC").Find(&list).Error
}

// GetByID 根据ID获取规格
func (serv *FoodSpecService) GetByID(ctx context.Context, id int64, merchantNo string) (*model.FoodSpecModel, error) {
	var spec model.FoodSpecModel

	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", id, merchantNo), &spec)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodSpecNotFound")
	}

	return &spec, nil
}

// Create 创建规格
func (serv *FoodSpecService) Create(ctx context.Context, req *request.CreateFoodSpecRequest, merchantNo string) (*model.FoodSpecModel, error) {
	// 检查名称是否已存在
	var count int64
	err := util.GetDB(ctx, serv.DB).Model(&model.FoodSpecModel{}).
		Where("merchant_no = ? AND (name_zh = ? OR name_ug = ?) AND deleted_at IS NULL", merchantNo, req.NameZh, req.NameUg).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "FoodSpecNameExists")
	}

	// 创建规格记录
	spec := &model.FoodSpecModel{
		MerchantNo: merchantNo,
		NameZh:     req.NameZh,
		NameUg:     req.NameUg,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	err = util.GetDB(ctx, serv.DB).Create(spec).Error
	if err != nil {
		return nil, err
	}

	return spec, nil
}

// Update 更新规格
func (serv *FoodSpecService) Update(ctx context.Context, id int64, req *request.UpdateFoodSpecRequest, merchantNo string) (*model.FoodSpecModel, error) {
	var spec model.FoodSpecModel

	// 查询规格记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", id, merchantNo), &spec)
	if err != nil {
		return nil, err
	}
	if !one {
		return nil, errors.NotFound("", "FoodSpecNotFound")
	}

	// 检查名称是否已存在（排除当前记录）
	var count int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodSpecModel{}).
		Where("merchant_no = ? AND id <> ? AND (name_zh = ? OR name_ug = ?) AND deleted_at IS NULL", merchantNo, id, req.NameZh, req.NameUg).
		Count(&count).Error
	if err != nil {
		return nil, err
	}
	if count > 0 {
		return nil, errors.BadRequest("", "FoodSpecNameExists")
	}

	// 更新规格信息
	spec.NameZh = req.NameZh
	spec.NameUg = req.NameUg
	spec.UpdatedAt = time.Now()

	err = util.GetDB(ctx, serv.DB).Save(&spec).Error
	if err != nil {
		return nil, err
	}

	return &spec, nil
}

// Delete 删除规格
func (serv *FoodSpecService) Delete(ctx context.Context, id int64, merchantNo string) error {
	var spec model.FoodSpecModel

	// 查询规格记录
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", id, merchantNo), &spec)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodSpecNotFound")
	}

	// 检查是否有关联的菜品
	var foodCount int64
	err = util.GetDB(ctx, serv.DB).Model(&model.FoodModel{}).
		Where("spec_id = ? AND deleted_at IS NULL", id).
		Count(&foodCount).Error
	if err != nil {
		return err
	}
	if foodCount > 0 {
		return errors.BadRequest("", "FoodSpecHasRelatedFoods")
	}

	// 软删除规格
	err = util.GetDB(ctx, serv.DB).Delete(&spec).Error
	if err != nil {
		return err
	}

	return nil
}

// GetFoodsBySpecID 根据规格ID获取关联的美食列表
func (serv *FoodSpecService) GetFoodsBySpecID(ctx context.Context, specID int64, merchantNo string) ([]*model.FoodModel, error) {
	var foods []*model.FoodModel

	db := util.GetDB(ctx, serv.DB).
		Where("spec_id = ? AND merchant_no = ? AND deleted_at IS NULL", specID, merchantNo).
		Preload("FoodCategory", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name_zh, name_ug").Where("deleted_at IS NULL")
		}).
		Preload("ParentFood", func(db *gorm.DB) *gorm.DB {
			return db.Select("id, name_zh, name_ug").Where("deleted_at IS NULL")
		})

	err := db.Find(&foods).Error
	if err != nil {
		return nil, err
	}

	return foods, nil
}

// SaveFoodSpecFoods 保存规格美食关联关系
func (serv *FoodSpecService) SaveFoodSpecFoods(ctx context.Context, req *request.SaveFoodSpecFoodsRequest, merchantNo string) error {
	// 验证规格是否存在
	var spec model.FoodSpecModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", req.SpecID, merchantNo), &spec)
	if err != nil {
		return err
	}
	if !one {
		return errors.NotFound("", "FoodSpecNotFound")
	}

	// 使用事务处理
	return serv.Trans.Exec(ctx, func(ctx context.Context) error {
		// 处理每个美食项
		for _, foodItem := range req.Foods {
			// 验证美食是否存在
			var parentFood model.FoodModel
			one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
				Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", foodItem.ID, merchantNo), &parentFood)
			if err != nil {
				return err
			}
			if !one {
				return errors.NotFound("", "FoodNotFound")
			}

			if parentFood.IsCombo {
				return errors.BadRequest("", "FoodIsCombo")
			}

			if parentFood.Type != model.FoodTypeFood {
				return errors.BadRequest("", "FoodTypeNotFood")
			}

			// 检查是否已存在该规格的美食记录
			var existingSpecFood model.FoodModel
			existingOne, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
				Where("pid = ? AND spec_id = ? AND merchant_no = ? AND deleted_at IS NULL",
					foodItem.ID, req.SpecID, merchantNo), &existingSpecFood)
			if err != nil {
				return err
			}

			if existingOne {
				// 如果存在，则更新现有记录
				existingSpecFood.NameZh = spec.NameZh           // 更新规格的中文名称
				existingSpecFood.NameUg = spec.NameUg           // 更新规格的维语名称
				existingSpecFood.CostPrice = foodItem.CostPrice // 更新成本价格
				existingSpecFood.Price = foodItem.Price         // 更新价格
				existingSpecFood.VipPrice = foodItem.VipPrice   // 更新会员价格
				existingSpecFood.UpdatedAt = time.Now()         // 更新时间戳

				err = util.GetDB(ctx, serv.DB).Save(&existingSpecFood).Error
				if err != nil {
					return err
				}
			} else {
				// 如果不存在，则创建新的规格美食记录
				specFood := &model.FoodModel{
					MerchantNo: merchantNo,
					Pid:        foodItem.ID,        // 使用原菜品ID作为父级ID
					SpecID:     req.SpecID,         // 规格ID
					Type:       model.FoodTypeFood, // 美食类型为1
					NameZh:     spec.NameZh,        // 使用规格的中文名称
					NameUg:     spec.NameUg,        // 使用规格的维语名称
					CostPrice:  foodItem.CostPrice, // 使用前端传入的成本价格
					Price:      foodItem.Price,     // 使用前端传入的价格
					VipPrice:   foodItem.VipPrice,  // 使用前端传入的会员价格
					// 继承原菜品的其他属性
					FoodCategoryID:   parentFood.FoodCategoryID,
					Image:            parentFood.Image,
					FormatID:         parentFood.FormatID,
					IsSpecialFood:    parentFood.IsSpecialFood,
					SupportScanOrder: parentFood.SupportScanOrder,
					IsCombo:          false,
					Sort:             parentFood.Sort, // 使用数据库默认值
					State:            parentFood.State,
					IsSync:           false,
				}

				err = util.GetDB(ctx, serv.DB).Create(specFood).Error
				if err != nil {
					return err
				}
			}
		}
		return nil
	})
}

// SaveSort 保存规格排序
func (serv *FoodSpecService) SaveSort(ctx context.Context, req *request.SaveFoodSpecSortRequest, merchantNo string) error {
	// 开启事务
	return util.GetDB(ctx, serv.DB).Transaction(func(tx *gorm.DB) error {
		for _, item := range req.Items {
			// 验证规格是否属于当前商家
			var count int64
			err := tx.Model(&model.FoodSpecModel{}).
				Where("id = ? AND merchant_no = ? AND deleted_at IS NULL", item.ID, merchantNo).
				Count(&count).Error
			if err != nil {
				return err
			}
			if count == 0 {
				return errors.BadRequest("", "FoodSpecNotFound")
			}

			// 更新排序
			err = tx.Model(&model.FoodSpecModel{}).
				Where("id = ? AND merchant_no = ?", item.ID, merchantNo).
				Update("sort", item.Sort).Error
			if err != nil {
				return err
			}
		}
		return nil
	})
}
